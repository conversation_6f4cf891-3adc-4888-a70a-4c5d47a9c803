package com.von.demo1.config;

import com.von.demo1.constant.MqConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ配置类
 *
 * <AUTHOR>
 * @Date 2025/08/05
 * @Version 1.0
 */
@Configuration
@Slf4j
public class RocketMQConfig {

    /**
     * 创建生产者Bean
     */
    @Bean
    public DefaultMQProducer defaultMQProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("default-producer-group");
        producer.setNamesrvAddr(MqConstant.NAME_SERVER);
        producer.setSendMsgTimeout(10000); // 10秒超时
        producer.setRetryTimesWhenSendFailed(3); // 发送失败重试次数
        producer.setRetryTimesWhenSendAsyncFailed(3); // 异步发送失败重试次数

        // 设置VIP通道（某些Docker环境需要）
        producer.setVipChannelEnabled(false);

        // 设置实例名称
        producer.setInstanceName("producer-" + System.currentTimeMillis());

        try {
            producer.start();
            log.info("RocketMQ Producer 启动成功，NameServer: {}", MqConstant.NAME_SERVER);
            log.info("Producer Group: {}", producer.getProducerGroup());
            log.info("Instance Name: {}", producer.getInstanceName());
        } catch (Exception e) {
            log.error("RocketMQ Producer 启动失败，请检查RocketMQ服务是否正常运行", e);
            log.error("NameServer地址: {}", MqConstant.NAME_SERVER);
            log.error("请确认Docker容器rocketmq-namesrv和rocketmq-broker正在运行");
        }

        return producer;
    }
}
