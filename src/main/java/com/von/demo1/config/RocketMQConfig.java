package com.von.demo1.config;

import com.von.demo1.constant.MqConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ配置类
 * 
 * <AUTHOR>
 * @Date 2025/08/05
 * @Version 1.0
 */
@Configuration
@Slf4j
public class RocketMQConfig {

    /**
     * 创建生产者Bean
     */
    @Bean
    public DefaultMQProducer defaultMQProducer() {
        DefaultMQProducer producer = new DefaultMQProducer("default-producer-group");
        producer.setNamesrvAddr(MqConstant.NAME_SERVER);
        producer.setSendMsgTimeout(10000); // 10秒超时
        producer.setRetryTimesWhenSendFailed(3); // 发送失败重试次数
        producer.setRetryTimesWhenSendAsyncFailed(3); // 异步发送失败重试次数
        
        try {
            producer.start();
            log.info("RocketMQ Producer 启动成功，NameServer: {}", MqConstant.NAME_SERVER);
        } catch (Exception e) {
            log.error("RocketMQ Producer 启动失败", e);
        }
        
        return producer;
    }
}
