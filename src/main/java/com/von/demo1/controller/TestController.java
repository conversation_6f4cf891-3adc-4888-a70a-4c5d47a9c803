package com.von.demo1.controller;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.von.demo1.enums.SexEnum;
import com.von.demo1.service.IPersonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.VersionType;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2025/6/8 23:04
 * @Version 1.0
 */
@RestController
@Slf4j
@RequiredArgsConstructor
public class TestController {

    private final RestHighLevelClient client;

    private final IPersonService personService;

    @GetMapping("/testPerson")
//    @DS("d1")
    @DSTransactional()
    public Object testPerson() throws Exception {


        return this.personService.list();
    }

    @GetMapping("/testEs")
    public Object testEs() throws Exception {
        log.info("client:{}", client);
        GetRequest request = new GetRequest("book", "1");
        request.fetchSourceContext(new FetchSourceContext(true, new String[]{"name", "price"}, new String[]{}));

        // 同步查询
        GetResponse response = this.client.get(request, RequestOptions.DEFAULT);
        if (response.isExists()) {
            System.out.println(response.getVersion());
            System.out.println(response.getSource());
        } else {
            System.out.println("文档不存在");
        }
        return response.getSourceAsString();


        // 异步查询
//        this.client.getAsync(request, RequestOptions.DEFAULT, new ActionListener<GetResponse>() {
//            @Override
//            public void onResponse(GetResponse documentFields) {
//                try {
//                	TimeUnit.SECONDS.sleep(2);
//                } catch (InterruptedException e) {
//                	e.printStackTrace();
//                }
//                System.out.println(documentFields.getSourceAsString());
//            }
//
//            @Override
//            public void onFailure(Exception e) {
//                e.printStackTrace();
//            }
//        });
//        System.out.println("你好世界");
//        return null;
    }

    @GetMapping("/testAdd")
    public Object testAdd() throws Exception {
        IndexRequest request = new IndexRequest("test_index");
        HashMap<String, Object> map = new HashMap<>();
        map.put("name", "VON");
        request.id("3");
        request.source(map);

        request.timeout(TimeValue.timeValueSeconds(1));
        request.timeout("1s");
        request.version(6);
        request.versionType(VersionType.EXTERNAL);


        IndexResponse response = this.client.index(request, RequestOptions.DEFAULT);
        System.out.println(response.getIndex());
        System.out.println(response.getId());
        System.out.println(response.getResult());


        return null;
    }

    @GetMapping("/testBulk")

    public Object testBulk() throws Exception {
        BulkRequest request = new BulkRequest();
        request.add(new IndexRequest("test_index_1").id("1").source("name", "VON-1a"));
        request.add(new IndexRequest("test_index_1").id("2").source("name", "VON-2a"));
        request.add(new IndexRequest("test_index_1").id("3").source("name", "VON-3a"));


        BulkResponse responses = this.client.bulk(request, RequestOptions.DEFAULT);
        for (BulkItemResponse responseItem : responses) {
            switch (responseItem.getOpType()) {
                case INDEX:
                    System.out.println("索引");
                    break;
                case CREATE:
                    System.out.println("创建");
                    break;
                case UPDATE:
                    System.out.println("更新");
                    break;
                case DELETE:
                    System.out.println("删除");
                    break;
                default:
                    System.out.println("默认");
                    break;
            }
        }

        return null;
    }

    @GetMapping("/testCreate")
    public Object testCreate() throws Exception {

        return null;
    }

    @PostMapping("/testUser")
    public Object testUser() throws Exception {
        SexEnum sexEnum = SexEnum.getSexEnum(null);

        return null;
    }

}
