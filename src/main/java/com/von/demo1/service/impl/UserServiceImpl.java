package com.von.demo1.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.demo1.mapper.UserMapper;
import com.von.demo1.model.User;
import com.von.demo1.service.IUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * User Service实现类
 * 
 * <AUTHOR>
 * @Date 2025/08/03
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    private final UserMapper userMapper;

    @Override
    public Page<User> selectByPage(Page<User> userPage) {
        Page<User> page = this.userMapper.selectByPage(userPage);
        return page;
    }
}