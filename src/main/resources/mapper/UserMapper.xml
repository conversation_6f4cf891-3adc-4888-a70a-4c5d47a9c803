<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.von.demo1.mapper.UserMapper">
    
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.von.demo1.model.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <select id="selectByPage" resultType="com.von.demo1.model.User">
        select * from user LEFT JOIN t_user_mobile_plan on user.id = t_user_mobile_plan.id
    </select>

</mapper>