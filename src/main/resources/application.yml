spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          username: root
          password: aa545554555..
          #url: ********************************************************************************************************************
          url: ****************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
        d1:
          username: root
          password: aa545554555..
          url: ***************************************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver