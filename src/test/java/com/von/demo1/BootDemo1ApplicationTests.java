package com.von.demo1;

import com.von.demo1.constant.MqConstant;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class BootDemo1ApplicationTests {

    @Test
    void contextLoads() {
        System.out.println("你好");

    }
    
    @Test
    public void test16() throws Exception {
        DefaultMQProducer producer = new DefaultMQProducer("test-producer-group");
        producer.setNamesrvAddr(MqConstant.NAME_SERVER);
        producer.start();
        Message message = new Message("testTopic", "莫伟雯".getBytes());
        SendResult result = producer.send(message);
        System.out.println(result.getSendStatus());
    }

}
