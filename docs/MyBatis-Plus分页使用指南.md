# MyBatis-Plus 分页插件使用指南

## 📖 目录
1. [分页原理](#分页原理)
2. [前后端交互流程](#前后端交互流程)
3. [完整代码示例](#完整代码示例)
4. [高级用法](#高级用法)
5. [性能优化](#性能优化)
6. [常见问题](#常见问题)

## 🔍 分页原理

### 核心机制
MyBatis-Plus分页插件通过**拦截器**机制实现分页功能：

```
1. 拦截SQL执行
2. 自动生成COUNT查询 → 获取总记录数
3. 修改原SQL添加LIMIT → 获取分页数据
4. 封装结果返回
```

### SQL转换示例
```sql
-- 原始查询
SELECT * FROM user WHERE status = 1

-- 自动生成的COUNT查询
SELECT COUNT(*) FROM user WHERE status = 1

-- 自动生成的分页查询
SELECT * FROM user WHERE status = 1 LIMIT 10, 10
```

## 🔄 前后端交互流程

### 前端请求参数
```javascript
// 前端发送的分页请求
{
  "current": 1,        // 当前页码（从1开始）
  "size": 10,          // 每页显示数量
  "username": "张三",   // 查询条件（可选）
  "status": 1          // 查询条件（可选）
}
```

### 后端返回数据
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [         // 当前页数据
      {
        "id": 1,
        "username": "张三",
        "password": "***"
      }
    ],
    "total": 100,        // 总记录数
    "size": 10,          // 每页显示数量
    "current": 1,        // 当前页码
    "pages": 10,         // 总页数
    "hasPrevious": false, // 是否有上一页
    "hasNext": true      // 是否有下一页
  }
}
```

## 💻 完整代码示例

### 1. 请求参数DTO
```java
@Data
public class UserPageRequest {
    @ApiModelProperty("当前页码")
    private Long current = 1L;
    
    @ApiModelProperty("每页显示数量")
    private Long size = 10L;
    
    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("状态")
    private Integer status;
}
```

### 2. Controller层
```java
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    
    private final IUserService userService;
    
    @PostMapping("/page")
    @ApiOperation("用户分页查询")
    public Result<IPage<User>> getUserPage(@RequestBody UserPageRequest request) {
        IPage<User> page = userService.getUserPage(request);
        return Result.success(page);
    }
}
```

### 3. Service层
```java
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    
    @Override
    public IPage<User> getUserPage(UserPageRequest request) {
        // 创建分页对象
        Page<User> page = new Page<>(request.getCurrent(), request.getSize());
        
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(request.getUsername()), 
                         User::getUsername, request.getUsername())
                   .eq(request.getStatus() != null, 
                      User::getStatus, request.getStatus())
                   .orderByDesc(User::getCreateTime);
        
        // 执行分页查询
        return this.page(page, queryWrapper);
    }
}
```

### 4. 前端调用示例
```javascript
// Vue.js示例
async getUserList() {
  try {
    const response = await this.$http.post('/user/page', {
      current: this.currentPage,
      size: this.pageSize,
      username: this.searchForm.username,
      status: this.searchForm.status
    });
    
    this.userList = response.data.records;
    this.total = response.data.total;
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
}
```

## 🚀 高级用法

### 1. 自定义分页查询
```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 自定义分页查询
     */
    IPage<UserVO> selectUserWithDept(Page<UserVO> page, @Param("status") Integer status);
}
```

```xml
<!-- UserMapper.xml -->
<select id="selectUserWithDept" resultType="com.von.demo1.vo.UserVO">
    SELECT u.*, d.name as deptName 
    FROM user u 
    LEFT JOIN department d ON u.dept_id = d.id 
    WHERE u.status = #{status}
    ORDER BY u.create_time DESC
</select>
```

### 2. 多表关联分页
```java
@Override
public IPage<UserVO> getUserWithDeptPage(UserPageRequest request) {
    Page<UserVO> page = new Page<>(request.getCurrent(), request.getSize());
    return userMapper.selectUserWithDept(page, request.getStatus());
}
```

### 3. 条件构造器分页
```java
@Override
public IPage<User> complexQuery(UserPageRequest request) {
    Page<User> page = new Page<>(request.getCurrent(), request.getSize());
    
    QueryWrapper<User> queryWrapper = new QueryWrapper<>();
    queryWrapper.select("id", "username", "email", "create_time")
               .like("username", request.getUsername())
               .between("create_time", request.getStartTime(), request.getEndTime())
               .in("status", Arrays.asList(1, 2))
               .orderByDesc("create_time");
    
    return this.page(page, queryWrapper);
}
```

## ⚡ 性能优化

### 1. 分页配置优化
```java
@Configuration
public class MybatisPlusConfig {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        // 设置数据库类型
        paginationInterceptor.setDbType(DbType.MYSQL);
        // 设置最大单页限制数量
        paginationInterceptor.setMaxLimit(1000L);
        // 优化COUNT查询
        paginationInterceptor.setOptimizeJoin(true);
        // 溢出处理
        paginationInterceptor.setOverflow(false);
        
        interceptor.addInnerInterceptor(paginationInterceptor);
        return interceptor;
    }
}
```

### 2. 索引优化建议
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_status_create_time ON user(status, create_time);
CREATE INDEX idx_user_dept_id ON user(dept_id);
```

### 3. 大数据量优化
```java
// 对于大数据量，可以禁用COUNT查询
Page<User> page = new Page<>(current, size, false); // 第三个参数为false
IPage<User> result = userService.page(page, queryWrapper);
```

## ❓ 常见问题

### Q1: 分页查询返回空数据？
**A:** 检查以下几点：
- 确认分页插件已正确配置
- 检查查询条件是否过于严格
- 验证数据库中是否有符合条件的数据

### Q2: COUNT查询性能差？
**A:** 优化方案：
- 开启`setOptimizeJoin(true)`优化JOIN查询
- 为WHERE条件字段添加索引
- 考虑使用缓存存储总数

### Q3: 页码溢出如何处理？
**A:** 两种处理方式：
```java
// 方式1：自动跳转到最后一页
paginationInterceptor.setOverflow(true);

// 方式2：返回空数据（推荐）
paginationInterceptor.setOverflow(false);
```

### Q4: 如何实现不查询总数的分页？
**A:** 
```java
// 创建分页对象时设置searchCount为false
Page<User> page = new Page<>(current, size, false);
```

## 📝 最佳实践

1. **参数校验**：对分页参数进行合理性校验
2. **默认值设置**：为current和size设置合理默认值
3. **最大限制**：设置单页最大查询数量，防止大量数据查询
4. **索引优化**：为常用查询字段建立合适的索引
5. **缓存策略**：对于变化不频繁的数据考虑使用缓存

---

> 💡 **提示**: 本文档基于MyBatis-Plus 3.5.3.1版本编写，不同版本可能存在差异。
